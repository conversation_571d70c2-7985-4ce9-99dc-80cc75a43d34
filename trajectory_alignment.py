#!/usr/bin/env python3
"""
轨迹对齐脚本：使用evo_ape工具对齐GPS轨迹和SLAM轨迹
1. 将GPS数据转换为TUM格式
2. 将SLAM数据转换为TUM格式
3. 使用evo_ape进行轨迹对齐
4. 计算比例因子
"""

import csv
import os
import subprocess
from pathlib import Path
import math

def convert_gps_to_tum(gps_file, output_file):
    """将GPS数据转换为TUM格式"""
    print(f"转换GPS数据: {gps_file} -> {output_file}")
    
    with open(gps_file, 'r', encoding='utf-8') as infile:
        reader = csv.reader(infile)
        rows = list(reader)
    
    header = rows[0]
    data_rows = rows[1:]
    
    print(f"GPS数据包含 {len(data_rows)} 行")
    
    # GPS数据格式：时间戳,纬度,经度,高度,俯仰角,滚转角,偏航角
    # TUM格式：timestamp x y z qx qy qz qw
    
    with open(output_file, 'w') as outfile:
        for i, row in enumerate(data_rows):
            timestamp = float(row[0]) / 1e9  # 转换为秒
            lat = float(row[1])  # 纬度
            lon = float(row[2])  # 经度
            alt = float(row[3])  # 高度
            pitch = math.radians(float(row[4]))  # 俯仰角转弧度
            roll = math.radians(float(row[5]))   # 滚转角转弧度
            yaw = math.radians(float(row[6]))    # 偏航角转弧度
            
            # 简化处理：将经纬度转换为相对坐标（米）
            # 这里使用简单的投影，实际应用中可能需要更精确的坐标转换
            if i == 0:
                lat0, lon0 = lat, lon
                x, y = 0.0, 0.0
            else:
                # 近似转换：1度纬度 ≈ 111km，1度经度 ≈ 111km * cos(lat)
                x = (lon - lon0) * 111000 * math.cos(math.radians(lat0))
                y = (lat - lat0) * 111000
            
            z = alt
            
            # 将欧拉角转换为四元数
            cy = math.cos(yaw * 0.5)
            sy = math.sin(yaw * 0.5)
            cp = math.cos(pitch * 0.5)
            sp = math.sin(pitch * 0.5)
            cr = math.cos(roll * 0.5)
            sr = math.sin(roll * 0.5)
            
            qw = cr * cp * cy + sr * sp * sy
            qx = sr * cp * cy - cr * sp * sy
            qy = cr * sp * cy + sr * cp * sy
            qz = cr * cp * sy - sr * sp * cy
            
            # 写入TUM格式
            outfile.write(f"{timestamp:.6f} {x:.6f} {y:.6f} {z:.6f} {qx:.6f} {qy:.6f} {qz:.6f} {qw:.6f}\n")
    
    print(f"GPS数据转换完成，保存到: {output_file}")

def convert_slam_to_tum(slam_file, output_file):
    """将SLAM数据转换为TUM格式"""
    print(f"转换SLAM数据: {slam_file} -> {output_file}")
    
    with open(slam_file, 'r') as infile:
        lines = infile.readlines()
    
    print(f"SLAM数据包含 {len(lines)} 行")
    
    with open(output_file, 'w') as outfile:
        for line in lines:
            parts = line.strip().split()
            if len(parts) == 8:
                timestamp = float(parts[0]) / 1e9  # 转换为秒
                x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                qx, qy, qz, qw = float(parts[4]), float(parts[5]), float(parts[6]), float(parts[7])
                
                # 写入TUM格式
                outfile.write(f"{timestamp:.6f} {x:.6f} {y:.6f} {z:.6f} {qx:.6f} {qy:.6f} {qz:.6f} {qw:.6f}\n")
    
    print(f"SLAM数据转换完成，保存到: {output_file}")

def run_evo_ape(ref_file, est_file, output_dir):
    """运行evo_ape进行轨迹对齐"""
    print(f"运行evo_ape轨迹对齐...")
    print(f"参考轨迹: {ref_file}")
    print(f"估计轨迹: {est_file}")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置PATH环境变量
    env = os.environ.copy()
    env['PATH'] = env['PATH'] + ':/home/<USER>/.local/bin'
    
    # 运行evo_ape命令（显示详细信息并生成图形）
    cmd = [
        '/home/<USER>/.local/bin/evo_ape',
        'tum',
        str(ref_file),
        str(est_file),
        '--align',
        '--correct_scale',
        '--save_results',
        str(output_dir / 'ape_results.zip'),
        '--plot',
        '--plot_mode', 'xyz',
        '--save_plot',
        str(output_dir / 'ape_plot.pdf'),
        '-v'  # 显示详细信息
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        print("evo_ape输出:")
        print(result.stdout)
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("evo_ape执行成功！")
            return True
        else:
            print(f"evo_ape执行失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"执行evo_ape时发生错误: {e}")
        return False

def main():
    # 文件路径
    base_path = Path("Datasets/dataset8/mav0/cam0")
    #gps_file = base_path / "5_slice_with_timestamps_height_pitch_cut.txt"
    gps_file = base_path / "49_slice_with_timestamps_pitch_yaw_cut.txt"
    slam_file = base_path / "f_dataset8_mono.txt"
    
    # 输出文件路径
    output_dir = base_path / "trajectory_alignment"
    gps_tum_file = output_dir / "gps_trajectory.tum"
    slam_tum_file = output_dir / "slam_trajectory.tum"
    
    print("=== 轨迹对齐分析 ===")
    print(f"GPS轨迹文件: {gps_file}")
    print(f"SLAM轨迹文件: {slam_file}")
    print(f"输出目录: {output_dir}")
    print("-" * 60)
    
    # 检查输入文件
    if not gps_file.exists():
        print(f"错误：GPS文件不存在: {gps_file}")
        return
    
    if not slam_file.exists():
        print(f"错误：SLAM文件不存在: {slam_file}")
        return
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 转换数据格式
    convert_gps_to_tum(gps_file, gps_tum_file)
    convert_slam_to_tum(slam_file, slam_tum_file)
    
    print("-" * 60)
    
    # 运行evo_ape
    success = run_evo_ape(gps_tum_file, slam_tum_file, output_dir)
    
    if success:
        print("-" * 60)
        print("轨迹对齐完成！")
        print(f"结果保存在: {output_dir}")
        print("\n关于你的问题:")
        print("1. evo_ape主要使用位置信息(x,y,z)和时间戳进行对齐")
        print("2. 姿态信息(qx,qy,qz,qw)在某些对齐算法中会用到，但主要还是位置")
        print("3. 比例因子会在对齐过程中自动计算并显示")
        print("4. --correct_scale参数会自动计算并应用比例因子")
    else:
        print("轨迹对齐失败！")

if __name__ == "__main__":
    main()
