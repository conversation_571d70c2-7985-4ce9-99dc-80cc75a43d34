#!/usr/bin/env python3
"""
脚本功能：处理49_slice_with_timestamps.txt文件
1. 将所有俯仰角减去30度
2. 将所有偏航角取反
3. 保存为新文件49_slice_with_timestamps_pitch_yaw.txt
"""

import csv
import os
from pathlib import Path

def process_file(input_file, output_file):
    """处理文件：修改俯仰角"""
    try:
        # 读取原始文件
        with open(input_file, 'r', encoding='utf-8') as infile:
            reader = csv.reader(infile)
            rows = list(reader)
        
        if not rows:
            print("错误：输入文件为空")
            return False
        
        # 获取标题行和数据行
        header = rows[0]
        data_rows = rows[1:]
        
        print(f"原始文件包含 {len(data_rows)} 行数据")
        
        # 检查列索引
        print(f"标题行: {header}")
        
        # 假设列的顺序：时间戳,纬度,经度,高度,俯仰角,滚转角,偏航角
        # 根据实际文件调整索引
        pitch_col = 4   # 俯仰角列（第5列，索引4）
        yaw_col = 6     # 偏航角列（第7列，索引6）

        if len(data_rows) == 0:
            print("错误：没有数据行")
            return False

        total_frames = len(data_rows)
        print(f"处理 {total_frames} 帧数据")
        print("将所有俯仰角减去30度，将所有偏航角取反")
        
        # 处理每一行数据
        processed_rows = []
        processed_rows.append(header)  # 添加标题行
        
        for i, row in enumerate(data_rows):
            new_row = row.copy()

            # 俯仰角减去30度
            try:
                original_pitch = float(row[pitch_col])
                new_pitch = original_pitch - 30.0
                new_row[pitch_col] = f"{new_pitch:.2f}"
            except (ValueError, IndexError):
                print(f"警告：第 {i+1} 行俯仰角数据异常: {row[pitch_col] if len(row) > pitch_col else '缺失'}")
                continue

            # 偏航角取反
            try:
                original_yaw = float(row[yaw_col])
                new_yaw = -original_yaw
                new_row[yaw_col] = f"{new_yaw:.2f}"
            except (ValueError, IndexError):
                print(f"警告：第 {i+1} 行偏航角数据异常: {row[yaw_col] if len(row) > yaw_col else '缺失'}")
                continue

            processed_rows.append(new_row)

            # 打印前几行和最后几行的处理信息
            if i < 3 or i >= total_frames - 3:
                print(f"  第 {i+1} 帧: 俯仰角 {original_pitch:.2f}° -> {new_pitch:.2f}° (减去30°), "
                      f"偏航角 {original_yaw:.2f}° -> {new_yaw:.2f}° (取反)")
        
        # 写入新文件
        with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            writer.writerows(processed_rows)
        
        print(f"成功生成新文件: {output_file}")
        print(f"处理了 {len(processed_rows)-1} 行数据")
        return True
        
    except FileNotFoundError:
        print(f"错误：找不到输入文件 {input_file}")
        return False
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return False

def main():
    # 文件路径
    base_path = Path("Datasets/dataset8/mav0/cam0")
    input_file = base_path / "49_slice_with_timestamps.txt"
    output_file = base_path / "49_slice_with_timestamps_pitch_yaw.txt"

    print("开始处理俯仰角和偏航角数据...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("-" * 60)
    
    # 检查输入文件是否存在
    if not input_file.exists():
        print(f"错误：输入文件不存在: {input_file}")
        return
    
    # 处理文件
    success = process_file(input_file, output_file)
    
    if success:
        print("-" * 60)
        print("处理完成！")
        print(f"新文件已保存为: {output_file}")
        print("\n修改内容:")
        print("1. 所有俯仰角: 减去30度")
        print("2. 所有偏航角: 取反")
    else:
        print("处理失败！")

if __name__ == "__main__":
    main()
