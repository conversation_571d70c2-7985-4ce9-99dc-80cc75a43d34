#!/usr/bin/env python3
"""
Trajectory Visualization Script (English Version)
Generate comparison plots for GPS and SLAM trajectories without Chinese font issues
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import subprocess
import os

def load_trajectory(tum_file):
    """Load trajectory from TUM format file"""
    timestamps = []
    positions = []
    
    with open(tum_file, 'r') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 4:
                timestamp = float(parts[0])
                x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                timestamps.append(timestamp)
                positions.append([x, y, z])
    
    return np.array(timestamps), np.array(positions)

def plot_trajectories_comparison(gps_file, slam_file, output_dir):
    """Plot trajectory comparison"""
    print("Generating trajectory comparison plot...")
    
    # Load trajectory data
    gps_timestamps, gps_positions = load_trajectory(gps_file)
    slam_timestamps, slam_positions = load_trajectory(slam_file)
    
    # Calculate scale factor (based on path length)
    def calculate_path_length(positions):
        distances = []
        for i in range(1, len(positions)):
            dist = np.linalg.norm(positions[i] - positions[i-1])
            distances.append(dist)
        return sum(distances)
    
    gps_length = calculate_path_length(gps_positions)
    slam_length = calculate_path_length(slam_positions)
    scale_factor = gps_length / slam_length if slam_length > 0 else 1.0
    
    # Scale SLAM trajectory
    slam_positions_scaled = slam_positions * scale_factor
    
    # Create figure
    fig = plt.figure(figsize=(15, 10))
    
    # 3D trajectory plot
    ax1 = fig.add_subplot(221, projection='3d')
    ax1.plot(gps_positions[:, 0], gps_positions[:, 1], gps_positions[:, 2], 
             'b-', linewidth=2, label='GPS Trajectory', alpha=0.8)
    ax1.plot(slam_positions_scaled[:, 0], slam_positions_scaled[:, 1], slam_positions_scaled[:, 2], 
             'r--', linewidth=2, label='SLAM Trajectory (Scaled)', alpha=0.8)
    ax1.set_xlabel('X (meters)')
    ax1.set_ylabel('Y (meters)')
    ax1.set_zlabel('Z (meters)')
    ax1.set_title('3D Trajectory Comparison')
    ax1.legend()
    ax1.grid(True)
    
    # XY plane plot
    ax2 = fig.add_subplot(222)
    ax2.plot(gps_positions[:, 0], gps_positions[:, 1], 'b-', linewidth=2, label='GPS Trajectory', alpha=0.8)
    ax2.plot(slam_positions_scaled[:, 0], slam_positions_scaled[:, 1], 'r--', linewidth=2, label='SLAM Trajectory (Scaled)', alpha=0.8)
    ax2.set_xlabel('X (meters)')
    ax2.set_ylabel('Y (meters)')
    ax2.set_title('XY Plane Trajectory Comparison')
    ax2.legend()
    ax2.grid(True)
    ax2.axis('equal')
    
    # Height comparison
    ax3 = fig.add_subplot(223)
    ax3.plot(gps_timestamps - gps_timestamps[0], gps_positions[:, 2], 'b-', linewidth=2, label='GPS Height', alpha=0.8)
    ax3.plot(slam_timestamps - slam_timestamps[0], slam_positions_scaled[:, 2], 'r--', linewidth=2, label='SLAM Height (Scaled)', alpha=0.8)
    ax3.set_xlabel('Time (seconds)')
    ax3.set_ylabel('Height (meters)')
    ax3.set_title('Height vs Time')
    ax3.legend()
    ax3.grid(True)
    
    # Path length comparison
    ax4 = fig.add_subplot(224)
    
    # Calculate cumulative path length
    def cumulative_path_length(positions):
        cumulative = [0]
        for i in range(1, len(positions)):
            dist = np.linalg.norm(positions[i] - positions[i-1])
            cumulative.append(cumulative[-1] + dist)
        return np.array(cumulative)
    
    gps_cumulative = cumulative_path_length(gps_positions)
    slam_cumulative = cumulative_path_length(slam_positions_scaled)
    
    ax4.plot(gps_timestamps - gps_timestamps[0], gps_cumulative, 'b-', linewidth=2, label='GPS Cumulative Distance', alpha=0.8)
    ax4.plot(slam_timestamps - slam_timestamps[0], slam_cumulative, 'r--', linewidth=2, label='SLAM Cumulative Distance (Scaled)', alpha=0.8)
    ax4.set_xlabel('Time (seconds)')
    ax4.set_ylabel('Cumulative Distance (meters)')
    ax4.set_title('Cumulative Path Length Comparison')
    ax4.legend()
    ax4.grid(True)
    
    # Add statistics information (smaller and positioned at top-left)
    info_text = f"""Trajectory Statistics:
GPS: {len(gps_positions)} poses, {gps_length:.2f}m
SLAM: {len(slam_positions)} poses, {slam_length:.2f}m
Scale: {scale_factor:.1f}
Duration: {gps_timestamps[-1] - gps_timestamps[0]:.1f}s"""

    # Position at top-left with smaller font and compact box
    fig.text(0.02, 0.98, info_text, fontsize=8, verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.3', facecolor='wheat', alpha=0.9))

    # Adjust layout to leave space for the statistics box
    plt.tight_layout(rect=[0, 0, 1, 0.85])
    
    # Save figure
    output_file = output_dir / 'trajectory_comparison_english.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Trajectory comparison plot saved to: {output_file}")
    
    # Show figure
    plt.show()
    
    return scale_factor

def run_evo_with_plot(gps_file, slam_file, output_dir):
    """Run evo tools and generate visualization results"""
    print("Running evo_ape for detailed analysis...")

    # Set environment variables
    env = os.environ.copy()
    env['PATH'] = env['PATH'] + ':/home/<USER>/.local/bin'

    # Run evo_ape
    cmd = [
        '/home/<USER>/.local/bin/evo_ape',
        'tum',
        str(gps_file),
        str(slam_file),
        '--align',
        '--correct_scale',
        '--plot',
        '--plot_mode', 'xyz',
        '--save_plot',
        str(output_dir / 'evo_ape_plot_english.pdf')
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        print("evo_ape output:")
        print(result.stdout)
        if result.stderr:
            print("Warning messages:")
            print(result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running evo_ape: {e}")
        return False

def run_evo_traj_plot(gps_file, slam_file, output_dir):
    """Run evo_traj to generate trajectory comparison plots"""
    print("Running evo_traj for trajectory comparison...")

    env = os.environ.copy()
    env['PATH'] = env['PATH'] + ':/home/<USER>/.local/bin'

    cmd = [
        '/home/<USER>/.local/bin/evo_traj',
        'tum',
        str(gps_file),
        str(slam_file),
        '--ref', str(gps_file),
        '--align',
        '--correct_scale',
        '--plot',
        '--plot_mode', 'xyz',
        '--save_plot',
        str(output_dir / 'evo_traj_plot.pdf')
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        print("evo_traj output:")
        print(result.stdout)
        if result.stderr:
            print("Warning messages:")
            print(result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running evo_traj: {e}")
        return False

def main():
    # File paths
    base_path = Path("Datasets/dataset8/mav0/cam0/trajectory_alignment")
    gps_file = base_path / "gps_trajectory.tum"
    slam_file = base_path / "slam_trajectory.tum"
    
    print("=== Trajectory Visualization Analysis ===")
    print(f"GPS trajectory file: {gps_file}")
    print(f"SLAM trajectory file: {slam_file}")
    print("-" * 60)
    
    # Check files
    if not gps_file.exists() or not slam_file.exists():
        print("Error: Trajectory files not found, please run trajectory_alignment.py first")
        return
    
    # Create custom comparison plot
    scale_factor = plot_trajectories_comparison(gps_file, slam_file, base_path)
    
    print("-" * 60)
    
    # Run evo tools to generate professional charts
    print("Generating professional analysis charts with evo tools...")
    run_evo_with_plot(gps_file, slam_file, base_path)
    run_evo_traj_plot(gps_file, slam_file, base_path)
    
    print("-" * 60)
    print("Visualization completed!")
    print(f"Scale factor: {scale_factor:.2f}")
    print(f"Results saved in: {base_path}")
    print("Generated files:")
    print("- trajectory_comparison_english.png (Custom comparison plot)")
    print("- evo_ape_plot_english.pdf (APE error analysis)")
    print("- evo_traj_plot.pdf (Trajectory comparison)")
    
    print("\nKey findings:")
    print(f"- SLAM trajectory needs to be multiplied by {scale_factor:.2f} to match real scale")
    print(f"- This means the SLAM output is scaled down by {1/scale_factor:.6f}")
    print("- The scale factor represents the ratio between real-world and SLAM distances")

if __name__ == "__main__":
    main()
