#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单目SLAM距离记录文件尺度恢复工具

该脚本用于将单目ORB-SLAM3生成的距离记录文件恢复到真实尺度。
单目SLAM由于缺乏深度信息，输出的坐标和距离都是相对尺度，
需要乘以比例因子来恢复真实的米制单位。

输入文件格式：
Frame_ID Timestamp Camera_X Camera_Y Camera_Z MapPoint_ID MapPoint_X MapPoint_Y MapPoint_Z Pixel_Coordinates Distance

处理内容：
- 无人机坐标 (Camera_X, Camera_Y, Camera_Z) × 比例因子
- 地图点坐标 (MapPoint_X, MapPoint_Y, MapPoint_Z) × 比例因子  
- 距离 (Distance) × 比例因子
- 像素坐标和其他信息保持不变

"""

import os
import argparse
from datetime import datetime

class DistanceRecordScaler:
    def __init__(self, scale_factor=212.544):
        """
        初始化距离记录缩放器
        
        Args:
            scale_factor (float): SLAM到真实世界的比例因子
        """
        self.scale_factor = scale_factor
        
    def parse_data_line(self, line):
        """
        解析数据行

        Args:
            line (str): 数据行

        Returns:
            dict: 解析后的数据字典，如果解析失败返回None
        """
        try:
            parts = line.strip().split()
            if len(parts) != 11:
                print(f"警告：数据行格式不正确，期望11个字段，实际{len(parts)}个：{line}")
                return None

            data = {
                'frame_id': int(parts[0]),
                'timestamp': float(parts[1]),
                'camera_x': float(parts[2]),
                'camera_y': float(parts[3]),
                'camera_z': float(parts[4]),
                'mappoint_id': int(parts[5]),
                'mappoint_x': float(parts[6]),
                'mappoint_y': float(parts[7]),
                'mappoint_z': float(parts[8]),
                'pixel_coords': parts[9],  # 保持字符串格式，不需要缩放
                'distance': float(parts[10])
            }
            return data

        except (ValueError, IndexError) as e:
            print(f"错误：解析数据行失败：{line} - {e}")
            return None
    
    def scale_data(self, data):
        """
        对数据应用比例因子
        
        Args:
            data (dict): 原始数据
            
        Returns:
            dict: 缩放后的数据
        """
        scaled_data = data.copy()
        
        # 缩放无人机坐标
        scaled_data['camera_x'] *= self.scale_factor
        scaled_data['camera_y'] *= self.scale_factor
        scaled_data['camera_z'] *= self.scale_factor
        
        # 缩放地图点坐标
        scaled_data['mappoint_x'] *= self.scale_factor
        scaled_data['mappoint_y'] *= self.scale_factor
        scaled_data['mappoint_z'] *= self.scale_factor
        
        # 缩放距离
        scaled_data['distance'] *= self.scale_factor
        
        return scaled_data
    
    def format_output_line(self, data):
        """
        格式化输出行
        
        Args:
            data (dict): 数据字典
            
        Returns:
            str: 格式化的输出行
        """
        return f"{data['frame_id']} {data['timestamp']:.6f} " \
               f"{data['camera_x']:.6f} {data['camera_y']:.6f} {data['camera_z']:.6f} " \
               f"{data['mappoint_id']} " \
               f"{data['mappoint_x']:.6f} {data['mappoint_y']:.6f} {data['mappoint_z']:.6f} " \
               f"{data['pixel_coords']} {data['distance']:.6f}"
    
    def process_file(self, input_file, output_file):
        """
        处理距离记录文件
        
        Args:
            input_file (str): 输入文件路径
            output_file (str): 输出文件路径
            
        Returns:
            bool: 处理是否成功
        """
        if not os.path.exists(input_file):
            print(f"错误：输入文件不存在：{input_file}")
            return False
        
        try:
            processed_lines = 0
            error_lines = 0
            
            with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
                # 写入文件头
                outfile.write("# ORB-SLAM3 Distance Recording File (Scaled to Real World)\n")
                outfile.write("# Format: Frame_ID Timestamp Camera_X Camera_Y Camera_Z MapPoint_ID MapPoint_X MapPoint_Y MapPoint_Z Pixel_Coordinates Distance\n")
                outfile.write("# Pixel_Coordinates format: normalized_u,normalized_v (range [0,1])\n")
                outfile.write("# Distances are sorted from nearest to farthest for each frame\n")
                outfile.write("# All coordinates and distances are scaled to real world units (meters)\n")
                outfile.write(f"# Scale factor applied: {self.scale_factor}\n")
                outfile.write(f"# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                
                for line_num, line in enumerate(infile, 1):
                    line = line.strip()
                    
                    # 跳过注释行和空行
                    if line.startswith('#') or not line:
                        continue
                    
                    # 解析数据行
                    data = self.parse_data_line(line)
                    if data is None:
                        error_lines += 1
                        continue
                    
                    # 应用比例因子
                    scaled_data = self.scale_data(data)
                    
                    # 写入缩放后的数据
                    output_line = self.format_output_line(scaled_data)
                    outfile.write(output_line + '\n')
                    
                    processed_lines += 1
                    
                    # 进度显示
                    if processed_lines % 1000 == 0:
                        print(f"已处理 {processed_lines} 行...")
            
            print(f"\n处理完成！")
            print(f"输入文件：{input_file}")
            print(f"输出文件：{output_file}")
            print(f"比例因子：{self.scale_factor}")
            print(f"成功处理：{processed_lines} 行")
            if error_lines > 0:
                print(f"错误行数：{error_lines} 行")
            
            return True
            
        except Exception as e:
            print(f"错误：处理文件时发生异常：{e}")
            return False
    
    def print_sample_comparison(self, input_file, output_file, num_samples=5):
        """
        打印样本对比，显示缩放前后的差异
        
        Args:
            input_file (str): 输入文件路径
            output_file (str): 输出文件路径
            num_samples (int): 显示的样本数量
        """
        print(f"\n{'='*80}")
        print("缩放前后对比示例")
        print(f"{'='*80}")
        
        try:
            # 读取原始文件的数据行
            original_lines = []
            with open(input_file, 'r') as f:
                for line in f:
                    if not line.startswith('#') and line.strip():
                        original_lines.append(line.strip())
                        if len(original_lines) >= num_samples:
                            break
            
            # 读取缩放后文件的数据行
            scaled_lines = []
            with open(output_file, 'r') as f:
                for line in f:
                    if not line.startswith('#') and line.strip():
                        scaled_lines.append(line.strip())
                        if len(scaled_lines) >= num_samples:
                            break
            
            # 显示对比
            for i in range(min(len(original_lines), len(scaled_lines), num_samples)):
                print(f"\n📊 样本 {i+1}:")
                
                # 解析原始数据
                orig_data = self.parse_data_line(original_lines[i])
                if orig_data:
                    print(f"   原始数据：")
                    print(f"     无人机位置: ({orig_data['camera_x']:.6f}, {orig_data['camera_y']:.6f}, {orig_data['camera_z']:.6f})")
                    print(f"     地图点位置: ({orig_data['mappoint_x']:.6f}, {orig_data['mappoint_y']:.6f}, {orig_data['mappoint_z']:.6f})")
                    print(f"     距离: {orig_data['distance']:.6f}")
                
                # 解析缩放后数据
                scaled_data = self.parse_data_line(scaled_lines[i])
                if scaled_data:
                    print(f"   缩放后数据：")
                    print(f"     无人机位置: ({scaled_data['camera_x']:.6f}, {scaled_data['camera_y']:.6f}, {scaled_data['camera_z']:.6f}) 米")
                    print(f"     地图点位置: ({scaled_data['mappoint_x']:.6f}, {scaled_data['mappoint_y']:.6f}, {scaled_data['mappoint_z']:.6f}) 米")
                    print(f"     距离: {scaled_data['distance']:.6f} 米")
                    
                    if orig_data:
                        print(f"   缩放倍数验证: {scaled_data['distance']/orig_data['distance']:.3f} (应该约等于 {self.scale_factor})")
                        
        except Exception as e:
            print(f"错误：无法显示对比示例：{e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='单目SLAM距离记录文件尺度恢复工具')
    parser.add_argument('-i', '--input', 
                       default='Datasets/dataset8/mav0/cam0/distances_dataset-dataset8_mono_1.txt',
                       help='输入距离记录文件路径')
    parser.add_argument('-o', '--output',
                       default='Datasets/dataset8/mav0/cam0/distances_dataset-dataset8_mono_1_scaled.txt',
                       help='输出缩放后文件路径')
    parser.add_argument('-s', '--scale', type=float, default=212.544,
                       help='比例因子 (默认: 212.544)')
    parser.add_argument('--show-comparison', action='store_true',
                       help='显示缩放前后的对比示例')
    
    args = parser.parse_args()
    
    print("单目SLAM距离记录文件尺度恢复工具")
    print("="*50)
    print(f"输入文件：{args.input}")
    print(f"输出文件：{args.output}")
    print(f"比例因子：{args.scale}")
    
    # 创建缩放器
    scaler = DistanceRecordScaler(scale_factor=args.scale)
    
    # 处理文件
    success = scaler.process_file(args.input, args.output)
    
    if success and args.show_comparison:
        scaler.print_sample_comparison(args.input, args.output)
    
    if success:
        print(f"\n✅ 处理成功！缩放后的文件已保存到：{args.output}")
    else:
        print(f"\n❌ 处理失败！")

if __name__ == "__main__":
    main()
