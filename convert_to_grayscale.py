#!/usr/bin/env python3
"""
灰度图转换脚本
功能：将 Datasets/dataset11 数据集中的彩色图像转换为灰度图像
特点：保持原有的目录结构和文件名不变，直接覆盖原始文件
"""

import cv2
import os
import glob
import numpy as np
from pathlib import Path

def convert_images_to_grayscale(dataset_path):
    """
    将指定数据集中的所有图像转换为灰度图像
    
    Args:
        dataset_path (str): 数据集的根路径
    """
    
    # 确保路径存在
    if not os.path.exists(dataset_path):
        print(f"错误：数据集路径 {dataset_path} 不存在")
        return False
    
    print(f"开始转换数据集：{dataset_path}")
    print("=" * 50)
    
    total_converted = 0
    
    # 处理 cam0 和 cam1 目录
    for cam_dir in ['cam0', 'cam1']:
        image_dir = os.path.join(dataset_path, f'mav0/{cam_dir}/data')
        
        if not os.path.exists(image_dir):
            print(f"目录 {image_dir} 不存在，跳过...")
            continue
            
        print(f"正在处理目录：{image_dir}")
        
        # 获取所有 PNG 文件
        image_files = glob.glob(os.path.join(image_dir, '*.png'))
        
        if not image_files:
            print(f"  - 在 {cam_dir} 中未找到 PNG 文件")
            continue
        
        converted_count = 0
        
        for i, image_file in enumerate(image_files):
            try:
                # 读取图像
                img = cv2.imread(image_file)
                if img is None:
                    print(f"  - 警告：无法读取文件 {os.path.basename(image_file)}")
                    continue
                
                # 检查图像是否已经是灰度图
                if len(img.shape) == 2 or (len(img.shape) == 3 and img.shape[2] == 1):
                    print(f"  - 跳过已是灰度图：{os.path.basename(image_file)}")
                    continue
                
                # 转换为灰度图
                gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                
                # 保存灰度图像（覆盖原始文件）
                success = cv2.imwrite(image_file, gray_img)
                
                if success:
                    converted_count += 1
                    # 每转换100张图像显示一次进度
                    if (i + 1) % 100 == 0:
                        print(f"  - 已转换 {i + 1}/{len(image_files)} 张图像...")
                else:
                    print(f"  - 错误：保存文件失败 {os.path.basename(image_file)}")
                    
            except Exception as e:
                print(f"  - 错误：处理文件 {os.path.basename(image_file)} 时发生异常：{e}")
                continue
        
        print(f"  - {cam_dir} 目录处理完成：成功转换 {converted_count}/{len(image_files)} 张图像")
        total_converted += converted_count
    
    print("=" * 50)
    print(f"转换完成！总共转换了 {total_converted} 张图像")
    return True

def verify_conversion(dataset_path):
    """
    验证转换结果，检查是否还有彩色图像
    
    Args:
        dataset_path (str): 数据集的根路径
    """
    print("\n验证转换结果...")
    
    for cam_dir in ['cam0', 'cam1']:
        image_dir = os.path.join(dataset_path, f'mav0/{cam_dir}/data')
        
        if not os.path.exists(image_dir):
            continue
            
        image_files = glob.glob(os.path.join(image_dir, '*.png'))
        color_count = 0
        
        for image_file in image_files:
            # 以灰度模式读取图像
            img_gray = cv2.imread(image_file, cv2.IMREAD_GRAYSCALE)
            img_color = cv2.imread(image_file, cv2.IMREAD_COLOR)
            
            if img_gray is not None and img_color is not None:
                # 检查彩色图像的三个通道是否完全相同
                b, g, r = cv2.split(img_color)
                if not (np.array_equal(b, g) and np.array_equal(g, r)):
                    color_count += 1
        
        if color_count == 0:
            print(f"✓ {cam_dir}: 所有图像均已转换为灰度图")
        else:
            print(f"✗ {cam_dir}: 还有 {color_count} 张彩色图像未转换")

if __name__ == "__main__":
    # 设置数据集路径（相对于脚本所在目录）
    dataset_path = "Datasets/dataset11"
    
    # 转换为绝对路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    full_dataset_path = os.path.join(script_dir, dataset_path)
    
    print("数据集灰度图转换工具")
    print(f"目标数据集：{full_dataset_path}")
    print(f"当前工作目录：{os.getcwd()}")
    
    # 执行转换
    success = convert_images_to_grayscale(full_dataset_path)
    
    if success:
        # 验证转换结果
        verify_conversion(full_dataset_path)
        print("\n转换任务完成！")
    else:
        print("\n转换任务失败！")
