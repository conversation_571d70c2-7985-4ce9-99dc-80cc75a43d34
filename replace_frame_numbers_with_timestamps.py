#!/usr/bin/env python3
"""
脚本功能：将5_slice.txt文件中的帧编号替换为对应的时间戳
- 读取dataset7.txt中的时间戳列表
- 读取5_slice.txt文件
- 将第一列的帧编号按顺序替换为对应的时间戳
- 生成新的文件5_slice_with_timestamps.txt
"""

import csv
import os
from pathlib import Path

def read_timestamps(timestamp_file):
    """读取时间戳文件，返回时间戳列表"""
    timestamps = []
    try:
        with open(timestamp_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:  # 跳过空行
                    timestamps.append(line)
        print(f"成功读取 {len(timestamps)} 个时间戳")
        return timestamps
    except FileNotFoundError:
        print(f"错误：找不到时间戳文件 {timestamp_file}")
        return None
    except Exception as e:
        print(f"读取时间戳文件时发生错误: {e}")
        return None

def process_slice_file(slice_file, timestamps, output_file):
    """处理5_slice.txt文件，替换帧编号为时间戳"""
    try:
        with open(slice_file, 'r', encoding='utf-8') as infile:
            # 先尝试直接读取文件内容
            content = infile.read()
            print(f"文件内容长度: {len(content)} 字符")

        # 重新打开文件进行CSV处理
        with open(slice_file, 'r', encoding='utf-8') as infile:
            reader = csv.reader(infile)

            # 读取所有行
            rows = list(reader)

        print(f"读取到 {len(rows)} 行数据")
        if not rows:
            print("错误：5_slice.txt文件为空")
            return False
            
        # 检查第一行是否为标题行
        header = rows[0]
        data_rows = rows[1:]
        
        print(f"原始文件包含 {len(data_rows)} 行数据")
        print(f"可用时间戳数量: {len(timestamps)}")
        
        # 检查数据行数是否与时间戳数量匹配
        if len(data_rows) > len(timestamps):
            print(f"警告：数据行数({len(data_rows)})超过时间戳数量({len(timestamps)})")
            print("将只处理前面匹配的行数")
            data_rows = data_rows[:len(timestamps)]
        
        # 创建新的数据行，替换第一列
        new_rows = []
        new_rows.append(['时间戳'] + header[1:])  # 修改标题行
        
        for i, row in enumerate(data_rows):
            if i < len(timestamps):
                # 替换第一列为对应的时间戳
                new_row = [timestamps[i]] + row[1:]
                new_rows.append(new_row)
                
                # 打印前几行的转换信息
                if i < 5:
                    print(f"  行 {i+1}: 帧编号 {row[0]} -> 时间戳 {timestamps[i]}")
        
        # 写入新文件
        with open(output_file, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            writer.writerows(new_rows)
            
        print(f"成功生成新文件: {output_file}")
        print(f"处理了 {len(data_rows)} 行数据")
        return True
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {slice_file}")
        return False
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return False

def main():
    # 文件路径
    base_path = Path("Datasets/dataset7/mav0/cam0")
    timestamp_file = base_path / "dataset7.txt"
    slice_file = base_path / "5_slice.txt"  # 文件在dataset目录下
    output_file = base_path / "5_slice_with_timestamps.txt"  # 输出到dataset目录
    
    print("开始处理帧编号到时间戳的转换...")
    print(f"时间戳文件: {timestamp_file}")
    print(f"输入文件: {slice_file}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    # 检查文件是否存在
    if not timestamp_file.exists():
        print(f"错误：时间戳文件不存在: {timestamp_file}")
        return
        
    if not slice_file.exists():
        print(f"错误：输入文件不存在: {slice_file}")
        return
    
    # 读取时间戳
    timestamps = read_timestamps(timestamp_file)
    if timestamps is None:
        return
    
    # 处理文件
    success = process_slice_file(slice_file, timestamps, output_file)
    
    if success:
        print("-" * 50)
        print("转换完成！")
        print(f"新文件已保存为: {output_file}")
    else:
        print("转换失败！")

if __name__ == "__main__":
    main()
