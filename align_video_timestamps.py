#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频时间戳对齐工具

该脚本用于对齐两个视频的时间戳，精确到帧级别。
适用场景：两部手机同时拍摄，但开始和结束时间略有差异。

主要功能：
1. 分析两个视频的基本信息（帧数、帧率、时长）
2. 手动或自动检测时间偏移
3. 精确裁剪视频，确保时间戳对齐
4. 生成对齐后的视频文件

作者：AI助手
日期：2025-08-01
"""

import cv2
import numpy as np
import os
import argparse
from datetime import datetime
import subprocess
import json

class VideoAligner:
    def __init__(self, video1_path, video2_path, output_dir="Video/aligned"):
        """
        初始化视频对齐器
        
        Args:
            video1_path (str): 第一个视频路径 (phone_camera_0.mp4)
            video2_path (str): 第二个视频路径 (phone_camera_1.mp4)
            output_dir (str): 输出目录
        """
        self.video1_path = video1_path
        self.video2_path = video2_path
        self.output_dir = output_dir
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 视频信息
        self.video1_info = None
        self.video2_info = None
        
        print("视频时间戳对齐工具")
        print("=" * 50)
        print(f"视频1: {video1_path}")
        print(f"视频2: {video2_path}")
        print(f"输出目录: {output_dir}")
    
    def get_video_info(self, video_path):
        """
        获取视频基本信息
        
        Args:
            video_path (str): 视频文件路径
            
        Returns:
            dict: 视频信息字典
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 获取视频属性
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps
        
        cap.release()
        
        info = {
            'path': video_path,
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration,
            'frame_time': 1.0 / fps  # 每帧时间间隔
        }
        
        return info
    
    def analyze_videos(self):
        """
        分析两个视频的基本信息
        """
        print("\n正在分析视频信息...")
        
        self.video1_info = self.get_video_info(self.video1_path)
        self.video2_info = self.get_video_info(self.video2_path)
        
        print(f"\n视频1 (phone_camera_0.mp4) 信息:")
        print(f"  分辨率: {self.video1_info['width']}x{self.video1_info['height']}")
        print(f"  帧率: {self.video1_info['fps']:.2f} FPS")
        print(f"  总帧数: {self.video1_info['frame_count']}")
        print(f"  时长: {self.video1_info['duration']:.2f} 秒")
        
        print(f"\n视频2 (phone_camera_1.mp4) 信息:")
        print(f"  分辨率: {self.video2_info['width']}x{self.video2_info['height']}")
        print(f"  帧率: {self.video2_info['fps']:.2f} FPS")
        print(f"  总帧数: {self.video2_info['frame_count']}")
        print(f"  时长: {self.video2_info['duration']:.2f} 秒")
        
        # 检查帧率是否一致
        if abs(self.video1_info['fps'] - self.video2_info['fps']) > 0.1:
            print(f"\n⚠️  警告: 两个视频的帧率不一致!")
            print(f"   视频1: {self.video1_info['fps']:.2f} FPS")
            print(f"   视频2: {self.video2_info['fps']:.2f} FPS")
    
    def manual_alignment(self):
        """
        手动设置对齐参数
        """
        print("\n手动对齐模式")
        print("-" * 30)
        print("根据您的描述:")
        print("- phone_camera_0.mp4 比 phone_camera_1.mp4 晚开始")
        print("- phone_camera_0.mp4 比 phone_camera_1.mp4 晚结束")
        print("- 需要裁剪 phone_camera_1.mp4 的开头")
        print("- 需要裁剪 phone_camera_0.mp4 的结尾")
        
        # 获取用户输入
        print(f"\n请输入需要从 phone_camera_1.mp4 开头裁剪的时间:")
        start_trim_seconds = float(input("裁剪秒数 (例如: 2.5): "))
        start_trim_frames = int(start_trim_seconds * self.video2_info['fps'])
        
        print(f"\n请输入需要从 phone_camera_0.mp4 结尾裁剪的时间:")
        end_trim_seconds = float(input("裁剪秒数 (例如: 1.8): "))
        end_trim_frames = int(end_trim_seconds * self.video1_info['fps'])
        
        return {
            'video1_start_frame': 0,
            'video1_end_frame': self.video1_info['frame_count'] - end_trim_frames,
            'video2_start_frame': start_trim_frames,
            'video2_end_frame': self.video2_info['frame_count'],
            'start_trim_seconds': start_trim_seconds,
            'end_trim_seconds': end_trim_seconds
        }
    
    def calculate_aligned_duration(self, alignment_params):
        """
        计算对齐后的视频时长
        """
        video1_frames = alignment_params['video1_end_frame'] - alignment_params['video1_start_frame']
        video2_frames = alignment_params['video2_end_frame'] - alignment_params['video2_start_frame']
        
        video1_duration = video1_frames / self.video1_info['fps']
        video2_duration = video2_frames / self.video2_info['fps']
        
        # 取较短的时长作为最终时长
        final_duration = min(video1_duration, video2_duration)
        final_frames_v1 = int(final_duration * self.video1_info['fps'])
        final_frames_v2 = int(final_duration * self.video2_info['fps'])
        
        return {
            'duration': final_duration,
            'video1_final_frames': final_frames_v1,
            'video2_final_frames': final_frames_v2
        }
    
    def trim_video_ffmpeg(self, input_path, output_path, start_frame, frame_count, fps):
        """
        使用FFmpeg精确裁剪视频
        """
        start_time = start_frame / fps
        duration = frame_count / fps
        
        cmd = [
            'ffmpeg', '-y',  # -y 覆盖输出文件
            '-i', input_path,
            '-ss', str(start_time),  # 开始时间
            '-t', str(duration),     # 持续时间
            '-c:v', 'libx264',       # 视频编码器
            '-c:a', 'aac',           # 音频编码器
            '-avoid_negative_ts', 'make_zero',  # 避免负时间戳
            output_path
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"FFmpeg错误: {result.stderr}")
            return False
        
        return True
    
    def align_videos(self, alignment_params):
        """
        执行视频对齐
        """
        print("\n开始对齐视频...")
        
        # 计算最终参数
        final_params = self.calculate_aligned_duration(alignment_params)
        
        print(f"对齐后时长: {final_params['duration']:.2f} 秒")
        print(f"视频1最终帧数: {final_params['video1_final_frames']}")
        print(f"视频2最终帧数: {final_params['video2_final_frames']}")
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output1 = os.path.join(self.output_dir, f"phone_camera_0_aligned_{timestamp}.mp4")
        output2 = os.path.join(self.output_dir, f"phone_camera_1_aligned_{timestamp}.mp4")
        
        # 裁剪视频1 (phone_camera_0.mp4)
        print(f"\n正在处理 {os.path.basename(self.video1_path)}...")
        success1 = self.trim_video_ffmpeg(
            self.video1_path, output1,
            alignment_params['video1_start_frame'],
            final_params['video1_final_frames'],
            self.video1_info['fps']
        )
        
        # 裁剪视频2 (phone_camera_1.mp4)
        print(f"\n正在处理 {os.path.basename(self.video2_path)}...")
        success2 = self.trim_video_ffmpeg(
            self.video2_path, output2,
            alignment_params['video2_start_frame'],
            final_params['video2_final_frames'],
            self.video2_info['fps']
        )
        
        if success1 and success2:
            print(f"\n✅ 视频对齐完成!")
            print(f"输出文件:")
            print(f"  {output1}")
            print(f"  {output2}")
            
            # 保存对齐参数
            self.save_alignment_report(alignment_params, final_params, output1, output2)
            
        else:
            print(f"\n❌ 视频对齐失败!")
    
    def save_alignment_report(self, alignment_params, final_params, output1, output2):
        """
        保存对齐报告
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'input_videos': {
                'video1': self.video1_path,
                'video2': self.video2_path
            },
            'output_videos': {
                'video1': output1,
                'video2': output2
            },
            'alignment_parameters': alignment_params,
            'final_parameters': final_params,
            'video_info': {
                'video1': self.video1_info,
                'video2': self.video2_info
            }
        }
        
        report_path = os.path.join(self.output_dir, f"alignment_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"对齐报告已保存: {report_path}")

def main():
    parser = argparse.ArgumentParser(description='视频时间戳对齐工具')
    parser.add_argument('--video1', default='Video/phone_camera_0.mp4', 
                       help='第一个视频路径 (默认: Video/phone_camera_0.mp4)')
    parser.add_argument('--video2', default='Video/phone_camera_1.mp4',
                       help='第二个视频路径 (默认: Video/phone_camera_1.mp4)')
    parser.add_argument('--output', default='Video/aligned',
                       help='输出目录 (默认: Video/aligned)')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.video1):
        print(f"错误: 找不到视频文件 {args.video1}")
        return
    
    if not os.path.exists(args.video2):
        print(f"错误: 找不到视频文件 {args.video2}")
        return
    
    # 创建对齐器
    aligner = VideoAligner(args.video1, args.video2, args.output)
    
    try:
        # 分析视频
        aligner.analyze_videos()
        
        # 手动对齐
        alignment_params = aligner.manual_alignment()
        
        # 执行对齐
        aligner.align_videos(alignment_params)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
