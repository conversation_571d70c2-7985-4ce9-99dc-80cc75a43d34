#!/usr/bin/env python3
import cv2
import sys

def check_video_info(video_path):
    """检查视频信息"""
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print(f"无法打开视频文件: {video_path}")
        return
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps if fps > 0 else 0
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"视频文件: {video_path}")
    print(f"分辨率: {width}x{height}")
    print(f"帧率: {fps} FPS")
    print(f"总帧数: {total_frames}")
    print(f"时长: {duration:.2f} 秒")
    
    cap.release()

if __name__ == "__main__":
    video_path = "Video/video_49_long_1_compress.mp4"
    check_video_info(video_path)
