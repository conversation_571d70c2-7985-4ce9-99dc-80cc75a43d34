#!/usr/bin/env python3
"""
计算SLAM轨迹的比例因子
通过比较GPS轨迹和SLAM轨迹的路径长度来估算比例因子
"""

import numpy as np
from pathlib import Path

def calculate_path_length(trajectory_file):
    """计算轨迹的总路径长度"""
    positions = []
    
    with open(trajectory_file, 'r') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 4:
                x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                positions.append([x, y, z])
    
    positions = np.array(positions)
    
    # 计算相邻点之间的距离
    distances = []
    for i in range(1, len(positions)):
        dist = np.linalg.norm(positions[i] - positions[i-1])
        distances.append(dist)
    
    total_length = sum(distances)
    return total_length, len(positions)

def main():
    # 文件路径
    base_path = Path("Datasets/dataset8/mav0/cam0/trajectory_alignment")
    gps_tum_file = base_path / "gps_trajectory.tum"
    slam_tum_file = base_path / "slam_trajectory.tum"
    
    print("=== SLAM轨迹比例因子计算 ===")
    print(f"GPS轨迹文件: {gps_tum_file}")
    print(f"SLAM轨迹文件: {slam_tum_file}")
    print("-" * 50)
    
    # 检查文件是否存在
    if not gps_tum_file.exists():
        print(f"错误：GPS轨迹文件不存在: {gps_tum_file}")
        return
    
    if not slam_tum_file.exists():
        print(f"错误：SLAM轨迹文件不存在: {slam_tum_file}")
        return
    
    # 计算路径长度
    gps_length, gps_poses = calculate_path_length(gps_tum_file)
    slam_length, slam_poses = calculate_path_length(slam_tum_file)
    
    print(f"GPS轨迹:")
    print(f"  位姿数量: {gps_poses}")
    print(f"  路径长度: {gps_length:.3f} 米")
    
    print(f"\nSLAM轨迹:")
    print(f"  位姿数量: {slam_poses}")
    print(f"  路径长度: {slam_length:.3f} 米")
    
    # 计算比例因子
    if slam_length > 0:
        scale_factor = gps_length / slam_length
        print(f"\n比例因子计算:")
        print(f"  GPS路径长度 / SLAM路径长度 = {gps_length:.3f} / {slam_length:.3f}")
        print(f"  比例因子 = {scale_factor:.6f}")
        
        print(f"\n这意味着:")
        print(f"  SLAM轨迹需要乘以 {scale_factor:.6f} 来匹配真实尺度")
        print(f"  SLAM轨迹相对于真实尺度缩放了 {1/scale_factor:.6f} 倍")
    else:
        print("错误：SLAM轨迹长度为0")
    
    print("-" * 50)
    print("注意：这是基于路径长度的简单估算")
    print("更精确的比例因子需要使用evo_ape的Sim(3)对齐结果")

if __name__ == "__main__":
    main()
