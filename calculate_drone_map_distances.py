#!/usr/bin/env python3
"""
无人机与地图点距离计算脚本

功能：
1. 读取无人机轨迹和地图点数据
2. 计算指定时间戳的无人机位置与所有地图点的距离
3. 应用比例因子将SLAM坐标转换为真实距离
4. 生成距离数据文件和可视化图片
5. 支持可配置的比例因子和时间戳索引

输入文件：
- f_dataset7_mono.txt: 无人机轨迹 (timestamp x y z qx qy qz qw)
- mp_dataset-dataset7_mono.txt: 地图点 (id x y z)

输出：
- distances_timestamp_N.txt: 距离数据文件
- distances_visualization_timestamp_N.png: 可视化图片
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd
from pathlib import Path
import argparse
import sys

class DroneMapDistanceCalculator:
    def __init__(self, scale_factor=527.2):
        """
        初始化距离计算器
        
        Args:
            scale_factor (float): SLAM到真实世界的比例因子，默认527.2
        """
        self.scale_factor = scale_factor
        self.drone_trajectory = None
        self.map_points = None
        
    def load_drone_trajectory(self, file_path):
        """加载无人机轨迹数据"""
        print(f"加载无人机轨迹: {file_path}")
        
        try:
            # 读取轨迹文件: timestamp x y z qx qy qz qw
            data = np.loadtxt(file_path)
            self.drone_trajectory = {
                'timestamps': data[:, 0],
                'positions': data[:, 1:4],  # x, y, z
                'quaternions': data[:, 4:8]  # qx, qy, qz, qw
            }
            print(f"成功加载 {len(self.drone_trajectory['timestamps'])} 个轨迹点")
            return True
            
        except Exception as e:
            print(f"加载轨迹文件失败: {e}")
            return False
    
    def load_map_points(self, file_path):
        """加载地图点数据"""
        print(f"加载地图点: {file_path}")
        
        try:
            # 读取地图点文件: id x y z
            data = np.loadtxt(file_path)
            self.map_points = {
                'ids': data[:, 0].astype(int),
                'positions': data[:, 1:4]  # x, y, z
            }
            print(f"成功加载 {len(self.map_points['ids'])} 个地图点")
            return True
            
        except Exception as e:
            print(f"加载地图点文件失败: {e}")
            return False
    
    def get_drone_pose_at_timestamp(self, timestamp_index):
        """获取指定时间戳索引的无人机位姿"""
        if self.drone_trajectory is None:
            raise ValueError("无人机轨迹数据未加载")
        
        if timestamp_index < 0 or timestamp_index >= len(self.drone_trajectory['timestamps']):
            raise ValueError(f"时间戳索引超出范围: {timestamp_index}")
        
        timestamp = self.drone_trajectory['timestamps'][timestamp_index]
        position = self.drone_trajectory['positions'][timestamp_index]
        quaternion = self.drone_trajectory['quaternions'][timestamp_index]
        
        # 应用比例因子转换为真实坐标
        real_position = position * self.scale_factor
        
        return {
            'timestamp': timestamp,
            'position': real_position,
            'quaternion': quaternion,
            'index': timestamp_index
        }
    
    def calculate_distances(self, drone_pose):
        """计算无人机与所有地图点的距离"""
        if self.map_points is None:
            raise ValueError("地图点数据未加载")
        
        drone_pos = drone_pose['position']
        
        # 应用比例因子转换地图点坐标
        map_positions_real = self.map_points['positions'] * self.scale_factor
        
        # 计算欧几里得距离
        distances = np.sqrt(np.sum((map_positions_real - drone_pos) ** 2, axis=1))
        
        # 创建结果数组
        results = []
        for i, (map_id, map_pos, distance) in enumerate(zip(
            self.map_points['ids'], 
            map_positions_real, 
            distances
        )):
            results.append({
                'map_id': map_id,
                'x': map_pos[0],
                'y': map_pos[1], 
                'z': map_pos[2],
                'distance': distance
            })
        
        # 按距离排序
        results.sort(key=lambda x: x['distance'])
        
        return results
    
    def save_distance_results(self, drone_pose, distances, output_file):
        """保存距离计算结果到文件"""
        print(f"保存距离结果到: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入头部信息
            f.write(f"# 无人机与地图点距离计算结果\n")
            f.write(f"# 时间戳索引: {drone_pose['index']}\n")
            f.write(f"# 时间戳: {drone_pose['timestamp']:.6f}\n")
            f.write(f"# 无人机位置 (米): X={drone_pose['position'][0]:.6f}, Y={drone_pose['position'][1]:.6f}, Z={drone_pose['position'][2]:.6f}\n")
            f.write(f"# 比例因子: {self.scale_factor}\n")
            f.write(f"# 地图点总数: {len(distances)}\n")
            f.write(f"# 格式: 地图点ID, X坐标(米), Y坐标(米), Z坐标(米), 距离(米)\n")
            f.write("#" + "="*80 + "\n")
            
            # 写入数据
            for result in distances:
                f.write(f"{result['map_id']:6d}, {result['x']:12.6f}, {result['y']:12.6f}, {result['z']:12.6f}, {result['distance']:12.6f}\n")
        
        # 统计信息
        distances_array = [r['distance'] for r in distances]
        print(f"距离统计:")
        print(f"  最近距离: {min(distances_array):.3f} 米")
        print(f"  最远距离: {max(distances_array):.3f} 米")
        print(f"  平均距离: {np.mean(distances_array):.3f} 米")
        print(f"  距离标准差: {np.std(distances_array):.3f} 米")

    def visualize_distances(self, drone_pose, distances, output_file):
        """生成距离可视化图片"""
        print(f"生成可视化图片: {output_file}")

        # 提取数据
        drone_pos = drone_pose['position']
        map_positions = np.array([[d['x'], d['y'], d['z']] for d in distances])
        distance_values = np.array([d['distance'] for d in distances])

        # 创建图形
        fig = plt.figure(figsize=(20, 15))

        # 1. 3D散点图
        ax1 = fig.add_subplot(2, 3, 1, projection='3d')

        # 绘制地图点，颜色表示距离（尺寸减小一半）
        scatter = ax1.scatter(map_positions[:, 0], map_positions[:, 1], map_positions[:, 2],
                            c=distance_values, cmap='RdYlGn_r', s=10, alpha=0.7)

        # 绘制无人机位置（改为红色圆点，尺寸适中）
        ax1.scatter(drone_pos[0], drone_pos[1], drone_pos[2],
                   c='red', s=50, marker='o', label=f'Drone Position')

        # 视觉坐标系：X右，Y下，Z前
        ax1.set_xlabel('X (meters) - Right')
        ax1.set_ylabel('Y (meters) - Down')
        ax1.set_zlabel('Z (meters) - Forward')
        ax1.set_title('3D View: Drone and Map Points (Visual Coordinate System)')

        # 反转Y轴使其向下
        ax1.invert_yaxis()

        ax1.legend()

        # 添加颜色条
        cbar1 = plt.colorbar(scatter, ax=ax1, shrink=0.5,pad=0.1)
        cbar1.set_label('Distance (meters)')

        # 2. XY平面投影
        ax2 = fig.add_subplot(2, 3, 2)
        scatter2 = ax2.scatter(map_positions[:, 0], map_positions[:, 1],
                             c=distance_values, cmap='RdYlGn_r', s=10, alpha=0.7)
        ax2.scatter(drone_pos[0], drone_pos[1], c='red', s=50, marker='o')
        ax2.set_xlabel('X (meters) - Right')
        ax2.set_ylabel('Y (meters) - Down')
        ax2.set_title('XY Plane Projection (Right-Down)')

        # 反转Y轴使其向下
        ax2.invert_yaxis()

        ax2.grid(True)
        cbar2 = plt.colorbar(scatter2, ax=ax2)
        cbar2.set_label('Distance (meters)')

        # 3. XZ平面投影
        ax3 = fig.add_subplot(2, 3, 3)
        scatter3 = ax3.scatter(map_positions[:, 0], map_positions[:, 2],
                             c=distance_values, cmap='RdYlGn_r', s=10, alpha=0.7)
        ax3.scatter(drone_pos[0], drone_pos[2], c='red', s=50, marker='o')
        ax3.set_xlabel('X (meters) - Right')
        ax3.set_ylabel('Z (meters) - Forward')
        ax3.set_title('XZ Plane Projection (Right-Forward)')
        ax3.grid(True)
        cbar3 = plt.colorbar(scatter3, ax=ax3)
        cbar3.set_label('Distance (meters)')

        # 4. YZ平面投影
        ax4 = fig.add_subplot(2, 3, 4)
        scatter4 = ax4.scatter(map_positions[:, 1], map_positions[:, 2],
                             c=distance_values, cmap='RdYlGn_r', s=10, alpha=0.7)
        ax4.scatter(drone_pos[1], drone_pos[2], c='red', s=50, marker='o')
        ax4.set_xlabel('Y (meters) - Down')
        ax4.set_ylabel('Z (meters) - Forward')
        ax4.set_title('YZ Plane Projection (Down-Forward)')

        # 反转Y轴使其向下
        ax4.invert_xaxis()  # 这里是X轴因为Y数据在X轴上显示

        ax4.grid(True)
        cbar4 = plt.colorbar(scatter4, ax=ax4)
        cbar4.set_label('Distance (meters)')

        # 5. 距离分布直方图
        ax5 = fig.add_subplot(2, 3, 5)
        n, bins, patches = ax5.hist(distance_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax5.set_xlabel('Distance (meters)')
        ax5.set_ylabel('Number of Map Points')
        ax5.set_title('Distance Distribution')
        ax5.grid(True, alpha=0.3)

        # 添加统计信息
        ax5.axvline(np.mean(distance_values), color='red', linestyle='--',
                   label=f'Mean: {np.mean(distance_values):.3f}m')
        ax5.axvline(np.median(distance_values), color='green', linestyle='--',
                   label=f'Median: {np.median(distance_values):.3f}m')
        ax5.legend()

        # 6. 最近地图点信息
        ax6 = fig.add_subplot(2, 3, 6)
        ax6.axis('off')

        # 显示详细信息
        info_text = f"""Drone-Map Points Distance Analysis

Timestamp Index: {drone_pose['index']}
Timestamp: {drone_pose['timestamp']:.6f}
Scale Factor: {self.scale_factor}

Drone Position (meters):
  X: {drone_pos[0]:.6f}
  Y: {drone_pos[1]:.6f}
  Z: {drone_pos[2]:.6f}

Distance Statistics:
  Total Map Points: {len(distances)}
  Min Distance: {min(distance_values):.3f} m
  Max Distance: {max(distance_values):.3f} m
  Mean Distance: {np.mean(distance_values):.3f} m
  Std Distance: {np.std(distance_values):.3f} m

Closest 5 Map Points:"""

        for i in range(min(5, len(distances))):
            d = distances[i]
            info_text += f"\n  {i+1}. ID:{d['map_id']:4d} Dist:{d['distance']:.3f}m"

        ax6.text(0.05, 0.95, info_text, transform=ax6.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"可视化图片已保存")
        plt.close()

def main():
    """主函数"""
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='计算无人机与地图点的距离')
    parser.add_argument('--timestamp_index', '-t', type=int, default=0,
                       help='时间戳索引 (默认: 0, 即第一个时间戳)')
    parser.add_argument('--scale_factor', '-s', type=float, default=212.544078,
                       help='SLAM到真实世界的比例因子 (默认: 212.544078)')
    parser.add_argument('--base_path', '-p', type=str,
                       default='Datasets/dataset8/mav0/cam0',
                       help='数据文件基础路径')
    parser.add_argument('--show_plot', action='store_true',
                       help='显示可视化图片')

    args = parser.parse_args()

    # 文件路径
    base_path = Path(args.base_path)
    #trajectory_file = base_path / "f_dataset7_mono.txt"
    trajectory_file = base_path / "f_dataset8_mono.txt"
    map_points_file = base_path / "mp_dataset-dataset8_mono.txt"

    # 输出目录
    output_dir = base_path / "distances"

    # 自动创建输出目录（包括父目录）
    try:
        output_dir.mkdir(parents=True, exist_ok=True)
        print(f"输出目录已准备: {output_dir}")
    except Exception as e:
        print(f"创建输出目录失败: {e}")
        sys.exit(1)

    # 输出文件名
    distance_file = output_dir / f"distances_timestamp_{args.timestamp_index}.txt"
    visualization_file = output_dir / f"distances_visualization_timestamp_{args.timestamp_index}.png"

    print("=" * 80)
    print("无人机与地图点距离计算")
    print("=" * 80)
    print(f"时间戳索引: {args.timestamp_index}")
    print(f"比例因子: {args.scale_factor}")
    print(f"轨迹文件: {trajectory_file}")
    print(f"地图点文件: {map_points_file}")
    print(f"输出目录: {output_dir}")
    print("-" * 80)

    # 检查输入文件
    if not trajectory_file.exists():
        print(f"错误: 轨迹文件不存在: {trajectory_file}")
        sys.exit(1)

    if not map_points_file.exists():
        print(f"错误: 地图点文件不存在: {map_points_file}")
        sys.exit(1)

    # 创建计算器
    calculator = DroneMapDistanceCalculator(scale_factor=args.scale_factor)

    # 加载数据
    if not calculator.load_drone_trajectory(trajectory_file):
        sys.exit(1)

    if not calculator.load_map_points(map_points_file):
        sys.exit(1)

    try:
        # 获取指定时间戳的无人机位姿
        drone_pose = calculator.get_drone_pose_at_timestamp(args.timestamp_index)
        print(f"\n无人机位姿信息:")
        print(f"  时间戳: {drone_pose['timestamp']:.6f}")
        print(f"  位置 (米): X={drone_pose['position'][0]:.6f}, Y={drone_pose['position'][1]:.6f}, Z={drone_pose['position'][2]:.6f}")

        # 计算距离
        print(f"\n计算与 {len(calculator.map_points['ids'])} 个地图点的距离...")
        distances = calculator.calculate_distances(drone_pose)

        # 保存结果
        calculator.save_distance_results(drone_pose, distances, distance_file)

        # 生成可视化
        calculator.visualize_distances(drone_pose, distances, visualization_file)

        print(f"\n处理完成!")
        print(f"距离数据文件: {distance_file}")
        print(f"可视化图片: {visualization_file}")

        # 显示最近的几个地图点
        print(f"\n最近的5个地图点:")
        for i in range(min(5, len(distances))):
            d = distances[i]
            print(f"  {i+1}. 地图点ID: {d['map_id']:4d}, 距离: {d['distance']:.3f} 米")

        if args.show_plot:
            plt.show()

    except ValueError as e:
        print(f"错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
