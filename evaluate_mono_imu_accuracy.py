#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单目+IMU模式精度评估工具

该脚本用于评估单目+IMU SLAM模式相对于单目+GPS对齐结果的精度。
以单目+GPS对齐的结果作为Ground Truth，分析单目+IMU模式的轨迹和距离误差。

主要功能：
1. 时间戳匹配（1ms容差）
2. 轨迹提取与7自由度对齐
3. 地图点匹配（基于像素坐标）
4. 轨迹误差分析
5. 距离误差分析（是单目+imu进行尺度缩放对齐单目+gps后的地图距离误差）
6. 可视化和报告生成

"""

import os
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import json
from datetime import datetime
from collections import defaultdict
import argparse

class MonoIMUEvaluator:
    def __init__(self, gt_file, imu_file, base_output_dir="Datasets/dataset8/mav0/cam0"):
        """
        初始化评估器

        Args:
            gt_file (str): Ground Truth文件路径（单目+GPS对齐结果）
            imu_file (str): 待评估文件路径（单目+IMU结果）
            base_output_dir (str): 基础输出目录
        """
        self.gt_file = gt_file
        self.imu_file = imu_file

        # 创建专门的评估结果文件夹
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.output_dir = os.path.join(base_output_dir, f"mono_imu_evaluation_{timestamp}")

        print(f"评估结果将保存到：{self.output_dir}")
        
        # 参数设置
        self.timestamp_tolerance = 0.001  # 1ms时间戳容差
        self.pixel_threshold = 0.02       # 像素坐标匹配阈值
        
        # 数据存储
        self.gt_data = {}
        self.imu_data = {}
        self.matched_timestamps = []
        self.matched_map_points = []
        
        # 轨迹数据
        self.trajectory_gt = []
        self.trajectory_imu = []
        self.trajectory_imu_aligned = []
        
        # 误差分析结果
        self.trajectory_errors = {}
        self.distance_errors = {}
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def parse_distance_file(self, file_path):
        """
        解析距离记录文件
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            dict: 按时间戳组织的数据字典
        """
        data = defaultdict(list)
        
        try:
            with open(file_path, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过注释行和空行
                    if line.startswith('#') or not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) != 11:
                        print(f"警告：{file_path} 第{line_num}行格式错误，跳过")
                        continue
                    
                    try:
                        record = {
                            'frame_id': int(parts[0]),
                            'timestamp': float(parts[1]),
                            'camera_x': float(parts[2]),
                            'camera_y': float(parts[3]),
                            'camera_z': float(parts[4]),
                            'mappoint_id': int(parts[5]),
                            'mappoint_x': float(parts[6]),
                            'mappoint_y': float(parts[7]),
                            'mappoint_z': float(parts[8]),
                            'pixel_coords': parts[9],  # "u,v"格式
                            'distance': float(parts[10])
                        }
                        
                        data[record['timestamp']].append(record)
                        
                    except ValueError as e:
                        print(f"错误：{file_path} 第{line_num}行数据解析失败：{e}")
                        continue
                        
        except Exception as e:
            print(f"错误：无法读取文件 {file_path}：{e}")
            return {}
        
        print(f"成功解析 {file_path}：{len(data)} 个时间戳，{sum(len(records) for records in data.values())} 条记录")
        return dict(data)
    
    def parse_pixel_coords(self, pixel_str):
        """
        解析像素坐标字符串
        
        Args:
            pixel_str (str): "u,v"格式的像素坐标
            
        Returns:
            tuple: (u, v)坐标
        """
        try:
            u, v = pixel_str.split(',')
            return float(u), float(v)
        except:
            return None, None
    
    def euclidean_distance_2d(self, p1, p2):
        """
        计算2D欧氏距离
        
        Args:
            p1, p2: (x, y)坐标点
            
        Returns:
            float: 欧氏距离
        """
        if None in p1 or None in p2:
            return float('inf')
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
    
    def match_timestamps(self):
        """
        匹配两个文件中的时间戳
        """
        print("正在匹配时间戳...")
        
        gt_timestamps = set(self.gt_data.keys())
        imu_timestamps = set(self.imu_data.keys())
        
        matched_pairs = []
        
        for gt_ts in gt_timestamps:
            best_match = None
            min_diff = float('inf')
            
            for imu_ts in imu_timestamps:
                diff = abs(gt_ts - imu_ts)
                if diff <= self.timestamp_tolerance and diff < min_diff:
                    min_diff = diff
                    best_match = imu_ts
            
            if best_match is not None:
                matched_pairs.append((gt_ts, best_match))
                imu_timestamps.remove(best_match)  # 避免重复匹配
        
        self.matched_timestamps = matched_pairs
        print(f"匹配到 {len(matched_pairs)} 个时间戳对")
        print(f"GT文件时间戳数：{len(gt_timestamps)}")
        print(f"IMU文件时间戳数：{len(self.imu_data)}")
        print(f"匹配率：{len(matched_pairs)/min(len(gt_timestamps), len(self.imu_data))*100:.1f}%")
    
    def extract_trajectories(self):
        """
        从匹配的时间戳中提取轨迹数据
        """
        print("正在提取轨迹数据...")

        # 按时间戳排序匹配对
        sorted_matches = sorted(self.matched_timestamps, key=lambda x: x[0])

        trajectory_data_gt = []
        trajectory_data_imu = []

        for gt_ts, imu_ts in sorted_matches:
            # 提取GT轨迹点（取第一个记录的相机位置）
            if gt_ts in self.gt_data and self.gt_data[gt_ts]:
                gt_record = self.gt_data[gt_ts][0]
                gt_pos = [gt_record['camera_x'], gt_record['camera_y'], gt_record['camera_z']]
                trajectory_data_gt.append((gt_ts, gt_pos))

            # 提取IMU轨迹点
            if imu_ts in self.imu_data and self.imu_data[imu_ts]:
                imu_record = self.imu_data[imu_ts][0]
                imu_pos = [imu_record['camera_x'], imu_record['camera_y'], imu_record['camera_z']]
                trajectory_data_imu.append((imu_ts, imu_pos))

        # 检查时间连续性
        if len(trajectory_data_gt) > 1:
            time_diffs = []
            for i in range(1, len(trajectory_data_gt)):
                time_diff = trajectory_data_gt[i][0] - trajectory_data_gt[i-1][0]
                time_diffs.append(time_diff)

            avg_time_diff = np.mean(time_diffs)
            max_time_diff = np.max(time_diffs)
            print(f"时间间隔统计 - 平均: {avg_time_diff:.3f}秒, 最大: {max_time_diff:.3f}秒")

            # 检查是否有异常大的时间跳跃
            large_gaps = [diff for diff in time_diffs if diff > avg_time_diff * 3]
            if large_gaps:
                print(f"警告: 发现 {len(large_gaps)} 个较大的时间间隔: {large_gaps}")

        # 提取位置数据
        self.trajectory_gt = np.array([pos for _, pos in trajectory_data_gt])
        self.trajectory_imu = np.array([pos for _, pos in trajectory_data_imu])
        self.trajectory_timestamps = [ts for ts, _ in trajectory_data_gt]

        print(f"提取轨迹点数：GT={len(self.trajectory_gt)}, IMU={len(self.trajectory_imu)}")
        print(f"时间范围：{self.trajectory_timestamps[0]:.3f} - {self.trajectory_timestamps[-1]:.3f} 秒")

        # 检查轨迹的空间连续性
        if len(self.trajectory_gt) > 1:
            gt_distances = []
            imu_distances = []
            gt_velocities = []
            imu_velocities = []

            for i in range(1, len(self.trajectory_gt)):
                gt_dist = np.linalg.norm(self.trajectory_gt[i] - self.trajectory_gt[i-1])
                imu_dist = np.linalg.norm(self.trajectory_imu[i] - self.trajectory_imu[i-1])
                gt_distances.append(gt_dist)
                imu_distances.append(imu_dist)

                # 计算速度 (距离/时间)
                time_diff = self.trajectory_timestamps[i] - self.trajectory_timestamps[i-1]
                if time_diff > 0:
                    gt_vel = gt_dist / time_diff
                    imu_vel = imu_dist / time_diff
                    gt_velocities.append(gt_vel)
                    imu_velocities.append(imu_vel)

            print(f"GT轨迹段距离 - 平均: {np.mean(gt_distances):.3f}m, 最大: {np.max(gt_distances):.3f}m")
            print(f"IMU轨迹段距离 - 平均: {np.mean(imu_distances):.3f}m, 最大: {np.max(imu_distances):.3f}m")

            if gt_velocities:
                print(f"GT轨迹速度 - 平均: {np.mean(gt_velocities):.3f}m/s, 最大: {np.max(gt_velocities):.3f}m/s")
                print(f"IMU轨迹速度 - 平均: {np.mean(imu_velocities):.3f}m/s, 最大: {np.max(imu_velocities):.3f}m/s")

            # 检查是否有异常大的跳跃
            large_jumps_gt = [dist for dist in gt_distances if dist > np.mean(gt_distances) * 3]
            large_jumps_imu = [dist for dist in imu_distances if dist > np.mean(imu_distances) * 3]

            if large_jumps_gt:
                print(f"警告: GT轨迹有 {len(large_jumps_gt)} 个大跳跃 (>3倍平均): {[f'{d:.3f}' for d in large_jumps_gt]}")
            if large_jumps_imu:
                print(f"警告: IMU轨迹有 {len(large_jumps_imu)} 个大跳跃 (>3倍平均): {[f'{d:.3f}' for d in large_jumps_imu]}")

            # 分析运动模式
            total_time = self.trajectory_timestamps[-1] - self.trajectory_timestamps[0]
            total_gt_distance = sum(gt_distances)
            total_imu_distance = sum(imu_distances)

            print(f"\n运动分析:")
            print(f"  总时间: {total_time:.3f} 秒")
            print(f"  GT总距离: {total_gt_distance:.3f} 米")
            print(f"  IMU总距离: {total_imu_distance:.3f} 米")
            print(f"  GT平均速度: {total_gt_distance/total_time:.3f} m/s")
            print(f"  IMU平均速度: {total_imu_distance/total_time:.3f} m/s")
    
    def align_trajectories(self):
        """
        使用Umeyama算法对齐轨迹
        """
        print("正在对齐轨迹...")

        if len(self.trajectory_gt) != len(self.trajectory_imu):
            print("错误：轨迹点数不匹配")
            return

        if len(self.trajectory_gt) < 3:
            print("错误：轨迹点数太少，无法进行对齐")
            return

        # 实现简化的Umeyama算法
        # 计算质心
        centroid_gt = np.mean(self.trajectory_gt, axis=0)
        centroid_imu = np.mean(self.trajectory_imu, axis=0)

        print(f"GT质心: {centroid_gt}")
        print(f"IMU质心: {centroid_imu}")

        # 去中心化
        gt_centered = self.trajectory_gt - centroid_gt
        imu_centered = self.trajectory_imu - centroid_imu

        # 计算协方差矩阵
        H = imu_centered.T @ gt_centered
        print(f"协方差矩阵H形状: {H.shape}")

        # SVD分解
        U, S, Vt = np.linalg.svd(H)
        print(f"SVD结果 - U形状: {U.shape}, S形状: {S.shape}, Vt形状: {Vt.shape}")

        # 计算旋转矩阵
        R = Vt.T @ U.T

        # 确保是右手坐标系
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T

        # 计算尺度 - 修复SVD返回值处理
        # S是奇异值的一维数组，需要正确计算尺度
        numerator = np.sum(S)  # 奇异值之和
        denominator = np.sum(imu_centered**2)  # IMU轨迹的总方差

        if denominator > 0:
            scale = numerator / denominator
        else:
            print("警告：IMU轨迹方差为0，使用默认尺度1.0")
            scale = 1.0

        # 计算平移
        t = centroid_gt - scale * R @ centroid_imu

        # 应用变换
        self.trajectory_imu_aligned = (scale * (self.trajectory_imu @ R.T)) + t

        print(f"对齐完成 - 尺度因子：{scale:.4f}")
        print(f"旋转矩阵行列式：{np.linalg.det(R):.4f}")
        print(f"平移向量：{t}")

        # 保存变换参数供地图点使用
        self.transformation = {
            'scale': scale,
            'rotation': R,
            'translation': t,
            'centroid_imu': centroid_imu
        }
    
    def match_map_points(self):
        """
        匹配地图点（基于像素坐标）
        """
        print("正在匹配地图点...")
        
        matched_points = []
        total_gt_points = 0
        total_imu_points = 0
        
        for gt_ts, imu_ts in self.matched_timestamps:
            gt_records = self.gt_data.get(gt_ts, [])
            imu_records = self.imu_data.get(imu_ts, [])
            
            total_gt_points += len(gt_records)
            total_imu_points += len(imu_records)
            
            # 为每个GT点寻找最佳匹配的IMU点
            for gt_record in gt_records:
                gt_pixel = self.parse_pixel_coords(gt_record['pixel_coords'])
                if None in gt_pixel:
                    continue
                
                best_match = None
                min_distance = float('inf')
                
                for imu_record in imu_records:
                    imu_pixel = self.parse_pixel_coords(imu_record['pixel_coords'])
                    if None in imu_pixel:
                        continue
                    
                    pixel_dist = self.euclidean_distance_2d(gt_pixel, imu_pixel)
                    
                    if pixel_dist <= self.pixel_threshold and pixel_dist < min_distance:
                        min_distance = pixel_dist
                        best_match = imu_record
                
                if best_match is not None:
                    matched_points.append({
                        'gt_record': gt_record,
                        'imu_record': best_match,
                        'pixel_distance': min_distance,
                        'timestamp_pair': (gt_ts, imu_ts)
                    })
        
        self.matched_map_points = matched_points
        
        print(f"地图点匹配完成：")
        print(f"  GT总地图点数：{total_gt_points}")
        print(f"  IMU总地图点数：{total_imu_points}")
        print(f"  成功匹配：{len(matched_points)} 个点对")
        print(f"  匹配率：{len(matched_points)/min(total_gt_points, total_imu_points)*100:.1f}%")
    
    def load_data(self):
        """
        加载和预处理数据
        """
        print("正在加载数据文件...")
        
        # 解析文件
        self.gt_data = self.parse_distance_file(self.gt_file)
        self.imu_data = self.parse_distance_file(self.imu_file)
        
        if not self.gt_data or not self.imu_data:
            raise ValueError("数据文件加载失败")
        
        # 匹配时间戳
        self.match_timestamps()
        
        if not self.matched_timestamps:
            raise ValueError("没有找到匹配的时间戳")
        
        # 提取轨迹
        self.extract_trajectories()
        
        # 对齐轨迹
        self.align_trajectories()
        
        # 匹配地图点
        self.match_map_points()
    
    def analyze_trajectory_errors(self):
        """
        分析轨迹误差
        """
        print("正在分析轨迹误差...")
        
        # 计算位置误差
        position_errors = np.linalg.norm(self.trajectory_gt - self.trajectory_imu_aligned, axis=1)
        
        # 计算轨迹长度
        def calculate_trajectory_length(trajectory):
            if len(trajectory) < 2:
                return 0
            diffs = np.diff(trajectory, axis=0)
            distances = np.linalg.norm(diffs, axis=1)
            return np.sum(distances)
        
        gt_length = calculate_trajectory_length(self.trajectory_gt)
        imu_length = calculate_trajectory_length(self.trajectory_imu_aligned)
        
        self.trajectory_errors = {
            'position_errors': position_errors.tolist(),
            'rmse': float(np.sqrt(np.mean(position_errors**2))),
            'mean_error': float(np.mean(position_errors)),
            'max_error': float(np.max(position_errors)),
            'min_error': float(np.min(position_errors)),
            'std_error': float(np.std(position_errors)),
            'trajectory_length_gt': float(gt_length),
            'trajectory_length_imu': float(imu_length),
            'length_error_abs': float(abs(gt_length - imu_length)),
            'length_error_ratio': float(abs(gt_length - imu_length) / gt_length * 100) if gt_length > 0 else 0
        }
        
        print(f"轨迹误差分析完成：")
        print(f"  RMSE: {self.trajectory_errors['rmse']:.3f} 米")
        print(f"  平均误差: {self.trajectory_errors['mean_error']:.3f} 米")
        print(f"  最大误差: {self.trajectory_errors['max_error']:.3f} 米")
        print(f"  GT轨迹长度: {self.trajectory_errors['trajectory_length_gt']:.3f} 米")
        print(f"  IMU轨迹长度: {self.trajectory_errors['trajectory_length_imu']:.3f} 米")
        print(f"  长度误差: {self.trajectory_errors['length_error_ratio']:.2f}%")

    def analyze_distance_errors(self):
        """
        分析距离误差(进行尺度缩放后的地图距离误差)
        """
        print("正在分析距离误差...")

        if not self.matched_map_points:
            print("警告：没有匹配的地图点，跳过距离误差分析")
            return

        gt_distances = []
        imu_distances = []
        absolute_errors = []
        relative_errors = []

        # 应用变换到IMU地图点坐标
        scale = self.transformation['scale']
        R = self.transformation['rotation']
        t = self.transformation['translation']
        centroid_imu = self.transformation['centroid_imu']

        for match in self.matched_map_points:
            gt_record = match['gt_record']
            imu_record = match['imu_record']

            # GT距离
            gt_dist = gt_record['distance']

            # 变换IMU地图点坐标
            imu_map_point = np.array([imu_record['mappoint_x'],
                                     imu_record['mappoint_y'],
                                     imu_record['mappoint_z']])
            imu_camera_pos = np.array([imu_record['camera_x'],
                                      imu_record['camera_y'],
                                      imu_record['camera_z']])

            # 应用相同的变换
            imu_map_point_aligned = scale * ((imu_map_point - centroid_imu) @ R.T) + centroid_imu * scale + t
            imu_camera_pos_aligned = scale * ((imu_camera_pos - centroid_imu) @ R.T) + centroid_imu * scale + t

            # 计算变换后的距离
            imu_dist_aligned = np.linalg.norm(imu_map_point_aligned - imu_camera_pos_aligned)

            # 计算误差
            abs_error = abs(gt_dist - imu_dist_aligned)
            rel_error = abs_error / gt_dist * 100 if gt_dist > 0 else 0

            gt_distances.append(gt_dist)
            imu_distances.append(imu_dist_aligned)
            absolute_errors.append(abs_error)
            relative_errors.append(rel_error)

        self.distance_errors = {
            'gt_distances': gt_distances,
            'imu_distances': imu_distances,
            'absolute_errors': absolute_errors,
            'relative_errors': relative_errors,
            'rmse': float(np.sqrt(np.mean(np.array(absolute_errors)**2))),
            'mean_error': float(np.mean(absolute_errors)),
            'max_error': float(np.max(absolute_errors)),
            'min_error': float(np.min(absolute_errors)),
            'std_error': float(np.std(absolute_errors)),
            'mean_relative_error': float(np.mean(relative_errors)),
            'max_relative_error': float(np.max(relative_errors))
        }

        print(f"距离误差分析完成：")
        print(f"  距离RMSE: {self.distance_errors['rmse']:.3f} 米")
        print(f"  平均距离误差: {self.distance_errors['mean_error']:.3f} 米")
        print(f"  最大距离误差: {self.distance_errors['max_error']:.3f} 米")
        print(f"  平均相对误差: {self.distance_errors['mean_relative_error']:.2f}%")
        print(f"  最大相对误差: {self.distance_errors['max_relative_error']:.2f}%")

    def visualize_trajectories(self):
        """
        可视化轨迹对比
        """
        print("正在生成轨迹可视化...")

        fig = plt.figure(figsize=(16, 12))

        # 3D轨迹对比
        ax1 = fig.add_subplot(2, 3, 1, projection='3d')

        # 绘制轨迹线
        ax1.plot(self.trajectory_gt[:, 0], self.trajectory_gt[:, 1], self.trajectory_gt[:, 2],
                'b-', linewidth=2, label='Ground Truth (Mono+GPS)', alpha=0.8)
        ax1.plot(self.trajectory_imu_aligned[:, 0], self.trajectory_imu_aligned[:, 1], self.trajectory_imu_aligned[:, 2],
                'r-', linewidth=2, label='Mono+IMU (Aligned)', alpha=0.8)

        # 标记起点和终点
        ax1.scatter(self.trajectory_gt[0, 0], self.trajectory_gt[0, 1], self.trajectory_gt[0, 2],
                   c='blue', s=100, marker='o', label='GT Start', alpha=0.9)
        ax1.scatter(self.trajectory_gt[-1, 0], self.trajectory_gt[-1, 1], self.trajectory_gt[-1, 2],
                   c='blue', s=100, marker='s', label='GT End', alpha=0.9)
        ax1.scatter(self.trajectory_imu_aligned[0, 0], self.trajectory_imu_aligned[0, 1], self.trajectory_imu_aligned[0, 2],
                   c='red', s=80, marker='o', alpha=0.9)
        ax1.scatter(self.trajectory_imu_aligned[-1, 0], self.trajectory_imu_aligned[-1, 1], self.trajectory_imu_aligned[-1, 2],
                   c='red', s=80, marker='s', alpha=0.9)

        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_zlabel('Z (m)')
        ax1.set_title('3D Trajectory Comparison')
        ax1.legend()
        ax1.grid(True)

        # XY平面投影
        ax2 = fig.add_subplot(2, 3, 2)
        ax2.plot(self.trajectory_gt[:, 0], self.trajectory_gt[:, 1],
                'b-', linewidth=2, label='Ground Truth', alpha=0.8)
        ax2.plot(self.trajectory_imu_aligned[:, 0], self.trajectory_imu_aligned[:, 1],
                'r-', linewidth=2, label='Mono+IMU', alpha=0.8)
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Y (m)')
        ax2.set_title('XY Plane Projection')
        ax2.legend()
        ax2.grid(True)
        ax2.axis('equal')

        # XZ平面投影
        ax3 = fig.add_subplot(2, 3, 3)
        ax3.plot(self.trajectory_gt[:, 0], self.trajectory_gt[:, 2],
                'b-', linewidth=2, label='Ground Truth', alpha=0.8)
        ax3.plot(self.trajectory_imu_aligned[:, 0], self.trajectory_imu_aligned[:, 2],
                'r-', linewidth=2, label='Mono+IMU', alpha=0.8)
        ax3.set_xlabel('X (m)')
        ax3.set_ylabel('Z (m)')
        ax3.set_title('XZ Plane Projection')
        ax3.legend()
        ax3.grid(True)

        # 位置误差随时间变化
        ax4 = fig.add_subplot(2, 3, 4)
        timestamps_idx = range(len(self.trajectory_errors['position_errors']))
        ax4.plot(timestamps_idx, self.trajectory_errors['position_errors'], 'g-', linewidth=1.5)
        ax4.set_xlabel('Time Index')
        ax4.set_ylabel('Position Error (m)')
        ax4.set_title('Position Error vs Time')
        ax4.grid(True)

        # 位置误差分布直方图
        ax5 = fig.add_subplot(2, 3, 5)
        ax5.hist(self.trajectory_errors['position_errors'], bins=30, alpha=0.7, color='green', edgecolor='black')
        ax5.set_xlabel('Position Error (m)')
        ax5.set_ylabel('Frequency')
        ax5.set_title('Position Error Distribution')
        ax5.grid(True, alpha=0.3)

        # 误差统计信息
        ax6 = fig.add_subplot(2, 3, 6)
        ax6.axis('off')
        stats_text = f"""Trajectory Error Statistics:

RMSE: {self.trajectory_errors['rmse']:.3f} m
Mean Error: {self.trajectory_errors['mean_error']:.3f} m
Max Error: {self.trajectory_errors['max_error']:.3f} m
Std Error: {self.trajectory_errors['std_error']:.3f} m

Trajectory Lengths:
GT: {self.trajectory_errors['trajectory_length_gt']:.3f} m
IMU: {self.trajectory_errors['trajectory_length_imu']:.3f} m
Length Error: {self.trajectory_errors['length_error_ratio']:.2f}%

Data Summary:
Matched Timestamps: {len(self.matched_timestamps)}
Matched Map Points: {len(self.matched_map_points)}"""

        ax6.text(0.1, 0.9, stats_text, transform=ax6.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()

        # 保存图片
        output_path = os.path.join(self.output_dir, 'mono_imu_trajectory_evaluation.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"轨迹对比图已保存到：{output_path}")

        return fig

    def visualize_distance_errors(self):
        """
        可视化距离误差分析
        """
        if not self.distance_errors or not self.distance_errors['gt_distances']:
            print("警告：没有距离误差数据，跳过距离误差可视化")
            return None

        print("正在生成距离误差可视化...")

        fig, axes = plt.subplots(2, 2, figsize=(12, 10))

        gt_distances = self.distance_errors['gt_distances']
        imu_distances = self.distance_errors['imu_distances']
        abs_errors = self.distance_errors['absolute_errors']
        rel_errors = self.distance_errors['relative_errors']

        # 距离对比散点图
        ax1 = axes[0, 0]
        ax1.scatter(gt_distances, imu_distances, alpha=0.6, s=20)
        min_dist = min(min(gt_distances), min(imu_distances))
        max_dist = max(max(gt_distances), max(imu_distances))
        ax1.plot([min_dist, max_dist], [min_dist, max_dist], 'r--', linewidth=2, label='Perfect Match')
        ax1.set_xlabel('Ground Truth Distance (m)')
        ax1.set_ylabel('Mono+IMU Distance (m)')
        ax1.set_title('Distance Comparison')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 绝对距离误差分布
        ax2 = axes[0, 1]
        ax2.hist(abs_errors, bins=30, alpha=0.7, color='orange', edgecolor='black')
        ax2.set_xlabel('Absolute Distance Error (m)')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Absolute Distance Error Distribution')
        ax2.grid(True, alpha=0.3)

        # 相对距离误差分布
        ax3 = axes[1, 0]
        ax3.hist(rel_errors, bins=30, alpha=0.7, color='purple', edgecolor='black')
        ax3.set_xlabel('Relative Distance Error (%)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Relative Distance Error Distribution')
        ax3.grid(True, alpha=0.3)

        # 距离误差统计信息
        ax4 = axes[1, 1]
        ax4.axis('off')

        stats_text = f"""Distance Error Statistics:

RMSE: {self.distance_errors['rmse']:.3f} m
Mean Error: {self.distance_errors['mean_error']:.3f} m
Max Error: {self.distance_errors['max_error']:.3f} m
Min Error: {self.distance_errors['min_error']:.3f} m
Std Error: {self.distance_errors['std_error']:.3f} m

Relative Errors:
Mean: {self.distance_errors['mean_relative_error']:.2f}%
Max: {self.distance_errors['max_relative_error']:.2f}%

Data Points: {len(gt_distances)}
Pixel Threshold: {self.pixel_threshold}"""

        ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()

        # 保存图片
        output_path = os.path.join(self.output_dir, 'mono_imu_distance_evaluation.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"距离误差分析图已保存到：{output_path}")

        return fig

    def generate_evaluation_report(self):
        """
        生成详细的评估报告
        """
        print("正在生成评估报告...")

        report = {
            'evaluation_info': {
                'gt_file': self.gt_file,
                'imu_file': self.imu_file,
                'timestamp_tolerance': self.timestamp_tolerance,
                'pixel_threshold': self.pixel_threshold,
                'evaluation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            'data_summary': {
                'total_timestamps_gt': len(self.gt_data),
                'total_timestamps_imu': len(self.imu_data),
                'matched_timestamps': len(self.matched_timestamps),
                'timestamp_matching_rate': len(self.matched_timestamps) / min(len(self.gt_data), len(self.imu_data)) * 100,
                'total_map_points_matched': len(self.matched_map_points),
                'map_point_matching_rate': len(self.matched_map_points) / sum(len(records) for records in self.gt_data.values()) * 100 if self.gt_data else 0
            },
            'trajectory_analysis': self.trajectory_errors,
            'distance_analysis': self.distance_errors if hasattr(self, 'distance_errors') else {},
            'transformation_parameters': {
                'scale_factor': float(self.transformation['scale']),
                'rotation_matrix': self.transformation['rotation'].tolist(),
                'translation_vector': self.transformation['translation'].tolist()
            }
        }

        # 保存JSON报告
        json_path = os.path.join(self.output_dir, 'mono_imu_evaluation_report.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # 生成文本报告
        txt_path = os.path.join(self.output_dir, 'mono_imu_evaluation_report.txt')
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("单目+IMU模式精度评估报告\n")
            f.write("=" * 50 + "\n\n")

            f.write("评估信息:\n")
            f.write(f"  Ground Truth文件: {report['evaluation_info']['gt_file']}\n")
            f.write(f"  待评估文件: {report['evaluation_info']['imu_file']}\n")
            f.write(f"  时间戳容差: {report['evaluation_info']['timestamp_tolerance']} 秒\n")
            f.write(f"  像素坐标阈值: {report['evaluation_info']['pixel_threshold']}\n")
            f.write(f"  评估时间: {report['evaluation_info']['evaluation_time']}\n\n")

            f.write("数据匹配统计:\n")
            f.write(f"  GT文件时间戳数: {report['data_summary']['total_timestamps_gt']}\n")
            f.write(f"  IMU文件时间戳数: {report['data_summary']['total_timestamps_imu']}\n")
            f.write(f"  匹配时间戳数: {report['data_summary']['matched_timestamps']}\n")
            f.write(f"  时间戳匹配率: {report['data_summary']['timestamp_matching_rate']:.2f}%\n")
            f.write(f"  匹配地图点数: {report['data_summary']['total_map_points_matched']}\n")
            f.write(f"  地图点匹配率: {report['data_summary']['map_point_matching_rate']:.2f}%\n\n")

            f.write("轨迹误差分析:\n")
            traj = report['trajectory_analysis']
            f.write(f"  RMSE: {traj['rmse']:.3f} 米\n")
            f.write(f"  平均误差: {traj['mean_error']:.3f} 米\n")
            f.write(f"  最大误差: {traj['max_error']:.3f} 米\n")
            f.write(f"  最小误差: {traj['min_error']:.3f} 米\n")
            f.write(f"  标准差: {traj['std_error']:.3f} 米\n")
            f.write(f"  GT轨迹长度: {traj['trajectory_length_gt']:.3f} 米\n")
            f.write(f"  IMU轨迹长度: {traj['trajectory_length_imu']:.3f} 米\n")
            f.write(f"  轨迹长度误差: {traj['length_error_ratio']:.2f}%\n\n")

            if report['distance_analysis']:
                f.write("距离误差分析:\n")
                dist = report['distance_analysis']
                f.write(f"  距离RMSE: {dist['rmse']:.3f} 米\n")
                f.write(f"  平均距离误差: {dist['mean_error']:.3f} 米\n")
                f.write(f"  最大距离误差: {dist['max_error']:.3f} 米\n")
                f.write(f"  最小距离误差: {dist['min_error']:.3f} 米\n")
                f.write(f"  距离误差标准差: {dist['std_error']:.3f} 米\n")
                f.write(f"  平均相对误差: {dist['mean_relative_error']:.2f}%\n")
                f.write(f"  最大相对误差: {dist['max_relative_error']:.2f}%\n\n")

            f.write("变换参数:\n")
            trans = report['transformation_parameters']
            f.write(f"  尺度因子: {trans['scale_factor']:.6f}\n")
            f.write(f"  旋转矩阵: {trans['rotation_matrix']}\n")
            f.write(f"  平移向量: {trans['translation_vector']}\n")

        print(f"评估报告已保存到：")
        print(f"  JSON格式: {json_path}")
        print(f"  文本格式: {txt_path}")

        return report

    def run_evaluation(self):
        """
        运行完整的评估流程
        """
        try:
            print("开始单目+IMU模式精度评估...")
            print("=" * 60)

            # 加载和预处理数据
            self.load_data()

            # 分析轨迹误差
            self.analyze_trajectory_errors()

            # 分析距离误差
            self.analyze_distance_errors()

            # 生成可视化
            self.visualize_trajectories()
            self.visualize_distance_errors()

            # 生成报告
            report = self.generate_evaluation_report()

            print("\n" + "=" * 60)
            print("评估完成！主要结果：")
            print(f"轨迹RMSE: {self.trajectory_errors['rmse']:.3f} 米")
            print(f"轨迹长度误差: {self.trajectory_errors['length_error_ratio']:.2f}%")
            if self.distance_errors:
                print(f"距离RMSE: {self.distance_errors['rmse']:.3f} 米")
                print(f"平均相对距离误差: {self.distance_errors['mean_relative_error']:.2f}%")
            print(f"时间戳匹配率: {len(self.matched_timestamps)/min(len(self.gt_data), len(self.imu_data))*100:.1f}%")
            print(f"地图点匹配数: {len(self.matched_map_points)}")

            return report

        except Exception as e:
            print(f"评估过程中发生错误：{e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='单目+IMU模式精度评估工具')
    parser.add_argument('--gt_file',
                       default='Datasets/dataset8/mav0/cam0/distances_dataset-dataset8_mono_scaled.txt',
                       help='Ground Truth文件路径（单目+GPS对齐结果）')
    parser.add_argument('--imu_file',
                       default='Datasets/dataset8/mav0/cam0/distances_dataset-dataset8_mono_1_scaled.txt',
                       help='待评估文件路径（单目+IMU结果）')
    parser.add_argument('--base_output_dir',
                       default='Datasets/dataset8/mav0/cam0',
                       help='基础输出目录（将在其中创建评估结果文件夹）')
    parser.add_argument('--timestamp_tolerance', type=float, default=0.001,
                       help='时间戳匹配容差（秒）')
    parser.add_argument('--pixel_threshold', type=float, default=0.02,
                       help='像素坐标匹配阈值')
    parser.add_argument('--show_plots', action='store_true',
                       help='显示可视化图表')

    args = parser.parse_args()

    print("单目+IMU模式精度评估工具")
    print("=" * 50)
    print(f"Ground Truth文件: {args.gt_file}")
    print(f"待评估文件: {args.imu_file}")
    print(f"基础输出目录: {args.base_output_dir}")
    print(f"时间戳容差: {args.timestamp_tolerance} 秒")
    print(f"像素坐标阈值: {args.pixel_threshold}")

    # 检查文件是否存在
    if not os.path.exists(args.gt_file):
        print(f"错误：Ground Truth文件不存在：{args.gt_file}")
        return

    if not os.path.exists(args.imu_file):
        print(f"错误：待评估文件不存在：{args.imu_file}")
        return

    # 创建评估器
    evaluator = MonoIMUEvaluator(
        gt_file=args.gt_file,
        imu_file=args.imu_file,
        base_output_dir=args.base_output_dir
    )

    # 设置参数
    evaluator.timestamp_tolerance = args.timestamp_tolerance
    evaluator.pixel_threshold = args.pixel_threshold

    # 运行评估
    report = evaluator.run_evaluation()

    if report:
        print(f"\n所有结果文件已保存到：{evaluator.output_dir}")

        if args.show_plots:
            plt.show()
    else:
        print("评估失败！")

if __name__ == "__main__":
    main()
